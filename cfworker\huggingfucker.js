addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

async function handleRequest(request) {
  // 处理 OPTIONS 请求以支持 CORS
  if (request.method === 'OPTIONS') {
    return handleOptionsRequest(request);
  }

  // 解析请求 URL
  const url = new URL(request.url);
  const targetHost = 'https://menyu-miaomiaoharem17.hf.space';
  const targetUrl = targetHost + url.pathname + url.search;

  // 创建代理请求
  const proxyRequest = new Request(targetUrl, {
    method: request.method,
    headers: new Headers(request.headers),
    body: request.body,
    redirect: 'follow'
  });

  // 删除跟踪相关的头部
  proxyRequest.headers.delete('Cookie');
  proxyRequest.headers.delete('X-Forwarded-For');
  proxyRequest.headers.delete('X-Real-IP');
  proxyRequest.headers.delete('User-Agent');
  proxyRequest.headers.delete('Accept');
  proxyRequest.headers.delete('Accept-Encoding');
  proxyRequest.headers.delete('Accept-Language');
  proxyRequest.headers.delete('Referer');
  proxyRequest.headers.delete('X-Requested-With');
  proxyRequest.headers.delete('Origin');
  proxyRequest.headers.delete('Connection');

  // 为每个请求生成一个新的临时 session ID
  const newSessionId = Math.random().toString(36).substring(2);
  proxyRequest.headers.set('Cookie', `session=${newSessionId}`);

  // 发送请求到目标 Space
  const response = await fetch(proxyRequest);

  // 检查是否为 WebSocket 升级请求
  if (request.headers.get('Upgrade') === 'websocket') {
    return response; // 直接返回 WebSocket 响应
  }

  // 创建代理响应
  const proxyResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: new Headers(response.headers)
  });

  // 删除服务器返回的 Set-Cookie，避免客户端保存 session
  proxyResponse.headers.delete('Set-Cookie');

  // 设置 CORS 头
  proxyResponse.headers.set('Access-Control-Allow-Origin', '*');
  proxyResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  proxyResponse.headers.set('Access-Control-Allow-Headers', '*');

  // 删除不适合反向代理的响应头
  proxyResponse.headers.delete('Content-Security-Policy');

  return proxyResponse;
}

// 处理 OPTIONS 预检请求
function handleOptionsRequest(request) {
  const headers = new Headers();
  headers.set('Access-Control-Allow-Origin', '*');
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  headers.set('Access-Control-Allow-Headers', '*');
  headers.set('Access-Control-Max-Age', '86400');

  return new Response(null, {
    status: 204,
    headers: headers
  });
}